package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.service.JsonMessageService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(DataController.class)
class DataControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private JsonMessageService jsonMessageService;

    @Test
    void testReceiveData() throws Exception {
        String jsonData = "{\"heartRate\": 72, \"timestamp\": \"2025-01-09T10:30:00\"}";
        
        mockMvc.perform(post("/api/data/1234/device001")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonData))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.message").value("Data received and stored successfully"));
    }

    @Test
    void testReceiveDataWithComplexJson() throws Exception {
        String jsonData = """
            {
                "patient": {
                    "id": "123",
                    "name": "John Doe"
                },
                "vitals": {
                    "heartRate": 72,
                    "bloodPressure": {
                        "systolic": 120,
                        "diastolic": 80
                    }
                },
                "timestamp": "2025-01-09T10:30:00"
            }
            """;
        
        mockMvc.perform(post("/api/data/2000/device002")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonData))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"));
    }
}
