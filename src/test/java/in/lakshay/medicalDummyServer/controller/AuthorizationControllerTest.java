package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.entity.DeviceAssociation;
import in.lakshay.medicalDummyServer.repository.DeviceAssociationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.main.lazy-initialization=true"
})
@Transactional
class AuthorizationControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private DeviceAssociationRepository deviceAssociationRepository;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // Create test device association
        DeviceAssociation association = new DeviceAssociation();
        association.setAccountNumber("1234");
        association.setDeviceId("************");
        association.setAuthToken("test-auth-token");
        association.setLoginUrl("https://rpm.510.test.safensound.io");
        association.setBaseUri("/oauth/token?accountNumber=1234&username=hubapi&Origin=EMR&Token=test");
        association.setIsActive(true);
        deviceAssociationRepository.save(association);
    }

    @Test
    void testSuccessfulDeviceAssociation() throws Exception {
        mockMvc.perform(get("/api/v1/associate/1234/************"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("FHIR"))
                .andExpect(jsonPath("$.version").value("1"))
                .andExpect(jsonPath("$.payload.authToken").value("test-auth-token"))
                .andExpect(jsonPath("$.payload.accountNumber").value("1234"))
                .andExpect(jsonPath("$.payload.errorMessage").doesNotExist());
    }

    @Test
    void testDeviceNotFound() throws Exception {
        mockMvc.perform(get("/api/v1/associate/1234/nonexistent"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("FHIR"))
                .andExpect(jsonPath("$.version").value("1"))
                .andExpect(jsonPath("$.payload.errorMessage").exists())
                .andExpect(jsonPath("$.payload.authToken").doesNotExist());
    }

    @Test
    void testInvalidAccountFormat() throws Exception {
        mockMvc.perform(get("/api/v1/associate/invalid/************"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("FHIR"))
                .andExpect(jsonPath("$.version").value("1"))
                .andExpect(jsonPath("$.payload.errorMessage").value("Invalid account number format. Must be 4 digits."))
                .andExpect(jsonPath("$.payload.authToken").doesNotExist());
    }
}
