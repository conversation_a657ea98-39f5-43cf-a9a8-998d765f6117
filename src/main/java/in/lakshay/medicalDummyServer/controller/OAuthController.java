package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.dto.JwtTokenResponse;
import in.lakshay.medicalDummyServer.service.JwtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;

@RestController
@RequestMapping("/oauth")
public class OAuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(OAuthController.class);
    
    @Autowired
    private JwtService jwtService;
    
    @RequestMapping(value = "/token", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseEntity<JwtTokenResponse> getToken(
            @RequestHeader("Authorization") String authHeader,
            @RequestParam String accountNumber,
            @RequestParam String username,
            @RequestParam String Origin,
            @RequestParam String Token) {
        
        logger.info("JWT token request for account: {} and username: {}", accountNumber, username);
        logger.debug("Authorization header: {}", authHeader);

        try {
            // Validate Authorization header
            if (authHeader == null || (!authHeader.startsWith("Basic ") && !authHeader.startsWith("basic "))) {
                logger.warn("Invalid Authorization header format: {}", authHeader);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
            
            // Extract and validate Basic auth credentials
            String base64Credentials = authHeader.substring(authHeader.indexOf(' ') + 1);
            String credentials;
            try {
                credentials = new String(Base64.getDecoder().decode(base64Credentials));
            } catch (IllegalArgumentException e) {
                logger.warn("Invalid Base64 encoding in Authorization header");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
            
            // For demo purposes, we'll accept any valid Basic auth format
            // In production, you would validate against actual credentials
            if (!credentials.contains(":")) {
                logger.warn("Invalid Basic auth format");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
            
            // Validate account number format
            if (!accountNumber.matches("\\d{4}")) {
                logger.warn("Invalid account number format: {}", accountNumber);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
            
            // Generate JWT tokens
            Long userId = 20950L; // Default user ID for demo
            String accessToken = jwtService.generateAccessToken(accountNumber, userId, username);
            String refreshToken = jwtService.generateRefreshToken(accountNumber, userId, username);
            long expiresIn = jwtService.getExpirationTime() / 1000; // Convert to seconds
            
            JwtTokenResponse response = new JwtTokenResponse(
                accountNumber, accessToken, refreshToken, expiresIn);
            
            logger.info("Successfully generated JWT tokens for account: {}", accountNumber);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error generating JWT token", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
