package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.entity.FhirObservation;
import in.lakshay.medicalDummyServer.entity.FhirCommunication;
import in.lakshay.medicalDummyServer.repository.FhirObservationRepository;
import in.lakshay.medicalDummyServer.repository.FhirCommunicationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {
    
    @Autowired
    private FhirObservationRepository observationRepository;
    
    @Autowired
    private FhirCommunicationRepository communicationRepository;
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "Medical Dummy Server",
            "version", "1.0.0"
        ));
    }
    
    @GetMapping("/observations")
    public ResponseEntity<List<FhirObservation>> getAllObservations() {
        return ResponseEntity.ok(observationRepository.findAll());
    }
    
    @GetMapping("/observations/account/{accountNumber}")
    public ResponseEntity<List<FhirObservation>> getObservationsByAccount(@PathVariable String accountNumber) {
        return ResponseEntity.ok(observationRepository.findByObservationTypeAndAccountNumber("NUMERIC", accountNumber));
    }
    
    @GetMapping("/observations/patient/{patientReference}")
    public ResponseEntity<List<FhirObservation>> getObservationsByPatient(
            @PathVariable String patientReference,
            @RequestParam String accountNumber) {
        return ResponseEntity.ok(observationRepository.findByPatientReferenceAndAccountNumber(patientReference, accountNumber));
    }
    
    @GetMapping("/communications")
    public ResponseEntity<List<FhirCommunication>> getAllCommunications() {
        return ResponseEntity.ok(communicationRepository.findAll());
    }
    
    @GetMapping("/communications/account/{accountNumber}")
    public ResponseEntity<List<FhirCommunication>> getCommunicationsByAccount(@PathVariable String accountNumber) {
        return ResponseEntity.ok(communicationRepository.findByStatusAndAccountNumber("inprogress", accountNumber));
    }
    
    @GetMapping("/sample-observation")
    public ResponseEntity<Map<String, Object>> getSampleObservation() {
        return ResponseEntity.ok(Map.of(
            "resourceType", "Observation",
            "id", "15774c77-7a81-11ed-897b-0e0713e7f861",
            "status", "final",
            "category", List.of(Map.of(
                "coding", List.of(Map.of(
                    "system", "http://terminology.hl7.org/CodeSystem/observation-category",
                    "code", "vital-signs",
                    "display", "Vital Signs"
                ))
            )),
            "subject", Map.of(
                "reference", "Patient/example"
            ),
            "effectiveDateTime", "2012-09-17T16:20:45+00:00",
            "performer", List.of(Map.of(
                "reference", "Practitioner/example"
            )),
            "component", List.of(
                Map.of(
                    "code", Map.of(
                        "coding", List.of(Map.of(
                            "system", "urn:oid:2.16.840.1.113883.6.24",
                            "code", "150017",
                            "display", "MDC_PRESS_BLD_SYS"
                        ))
                    ),
                    "valueQuantity", Map.of(
                        "value", 107,
                        "unit", "mmHg",
                        "system", "http://unitsofmeasure.org",
                        "code", "mm[Hg]"
                    )
                ),
                Map.of(
                    "code", Map.of(
                        "coding", List.of(Map.of(
                            "system", "urn:oid:2.16.840.1.113883.6.24",
                            "code", "147842",
                            "display", "MDC_ECG_HEART_RATE"
                        ))
                    ),
                    "valueQuantity", Map.of(
                        "value", 44,
                        "unit", "beats/minute",
                        "system", "http://unitsofmeasure.org",
                        "code", "/min"
                    )
                )
            )
        ));
    }
    
    @GetMapping("/sample-communication")
    public ResponseEntity<Map<String, Object>> getSampleCommunication() {
        return ResponseEntity.ok(Map.of(
            "resourceType", "Communication",
            "id", "3dc1d518-f07a-4b0c-b0a3-a4de16ff0af3",
            "status", "inprogress",
            "category", List.of(Map.of(
                "coding", List.of(Map.of(
                    "system", "http://hl7.org/fhir/ValueSet/communication-category",
                    "code", "alert",
                    "display", "Alert"
                )),
                "text", "Device Alert"
            )),
            "subject", Map.of(
                "reference", "Patient/4586787",
                "display", "Test Bb"
            ),
            "sent", "2025-01-21T11:03:30-06:00",
            "sender", Map.of(
                "reference", "Device/238795634122069"
            ),
            "payload", List.of(
                Map.of("contentString", "Error: OutOfRange"),
                Map.of("contentString", "Alert Priority: LOW"),
                Map.of("contentString", "StartTime: 2025-01-21T11:03:30-06:00"),
                Map.of("contentString", "Type: anne-chest"),
                Map.of("contentString", "ID: CK1037")
            )
        ));
    }
}
