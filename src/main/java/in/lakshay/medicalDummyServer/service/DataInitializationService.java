package in.lakshay.medicalDummyServer.service;

import in.lakshay.medicalDummyServer.entity.DeviceAssociation;
import in.lakshay.medicalDummyServer.entity.Patient;
import in.lakshay.medicalDummyServer.entity.Device;
import in.lakshay.medicalDummyServer.repository.DeviceAssociationRepository;
import in.lakshay.medicalDummyServer.repository.PatientRepository;
import in.lakshay.medicalDummyServer.repository.DeviceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.Base64;

@Service
@Profile("!test")
public class DataInitializationService implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializationService.class);
    
    @Autowired
    private DeviceAssociationRepository deviceAssociationRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private DeviceRepository deviceRepository;
    
    @Value("${fhir.base-url}")
    private String baseUrl;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("Initializing sample data...");
        
        createSampleDeviceAssociations();
        createSamplePatients();
        createSampleDevices();
        
        logger.info("Sample data initialization completed");
    }
    
    private void createSampleDeviceAssociations() {
        // Create sample device associations for testing
        String[] accounts = {"1234", "2000", "5678"};
        String[] deviceIds = {"************", "***************", "***************"};
        
        for (int i = 0; i < accounts.length; i++) {
            String account = accounts[i];
            String deviceId = deviceIds[i];
            
            if (!deviceAssociationRepository.existsByAccountNumberAndDeviceIdAndIsActive(account, deviceId, true)) {
                DeviceAssociation association = new DeviceAssociation();
                association.setAccountNumber(account);
                association.setDeviceId(deviceId);
                
                // Generate a sample auth token (Base64 encoded)
                String credentials = String.format("device_%s:password_%s", deviceId, account);
                String authToken = Base64.getEncoder().encodeToString(credentials.getBytes());
                association.setAuthToken(authToken);
                
                association.setLoginUrl(baseUrl);
                association.setBaseUri(String.format("/oauth/token?accountNumber=%s&username=hubapi&Origin=EMR&Token=sample_token", account));
                association.setIsActive(true);
                
                deviceAssociationRepository.save(association);
                logger.info("Created device association for account: {} and device: {}", account, deviceId);
            }
        }
    }
    
    private void createSamplePatients() {
        // Create sample patients
        String[][] patients = {
            {"Patient/f001", "P. van de Heuvel", "1234"},
            {"Patient/example", "John Doe", "2000"},
            {"Patient/4586787", "Test Bb", "2000"},
            {"Patient/*********", "Jane Smith", "1234"}
        };
        
        for (String[] patientData : patients) {
            String patientReference = patientData[0];
            String displayName = patientData[1];
            String accountNumber = patientData[2];
            
            if (!patientRepository.existsByPatientReferenceAndAccountNumberAndIsActive(patientReference, accountNumber, true)) {
                Patient patient = new Patient();
                patient.setPatientReference(patientReference);
                patient.setDisplayName(displayName);
                patient.setAccountNumber(accountNumber);
                patient.setIsActive(true);
                
                patientRepository.save(patient);
                logger.info("Created patient: {} for account: {}", patientReference, accountNumber);
            }
        }
    }
    
    private void createSampleDevices() {
        // Create sample devices
        String[][] devices = {
            {"Device/15774c77-7a81-11ed-897b-0e0713e7f869", "12 lead EKG Device Metric", "15774c77-7a81-11ed-897b-0e0713e7f869", "1234"},
            {"Device/***************", "HUB device", "***************", "2000"},
            {"Device/***************", "Monitoring Device", "***************", "2000"}
        };
        
        for (String[] deviceData : devices) {
            String deviceReference = deviceData[0];
            String displayName = deviceData[1];
            String deviceUuid = deviceData[2];
            String accountNumber = deviceData[3];
            
            if (!deviceRepository.existsByDeviceReferenceAndAccountNumberAndIsActive(deviceReference, accountNumber, true)) {
                Device device = new Device();
                device.setDeviceReference(deviceReference);
                device.setDisplayName(displayName);
                device.setDeviceUuid(deviceUuid);
                device.setAccountNumber(accountNumber);
                device.setIsActive(true);
                
                deviceRepository.save(device);
                logger.info("Created device: {} for account: {}", deviceReference, accountNumber);
            }
        }
    }
}
