package in.lakshay.medicalDummyServer.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class OperationOutcomeService {
    
    private static final Logger logger = LoggerFactory.getLogger(OperationOutcomeService.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public String createSuccessResponse(String messageId, String message) {
        try {
            ObjectNode response = objectMapper.createObjectNode();
            response.put("resourceType", "OperationOutcome");
            response.put("id", messageId != null ? messageId : "unknown");
            
            ArrayNode issues = objectMapper.createArrayNode();
            ObjectNode issue = objectMapper.createObjectNode();
            issue.put("severity", "information");
            
            ObjectNode details = objectMapper.createObjectNode();
            details.put("text", message);
            issue.set("details", details);
            
            issues.add(issue);
            response.set("issue", issues);
            
            String jsonResponse = objectMapper.writeValueAsString(response);
            logger.debug("Created success response: {}", jsonResponse);
            return jsonResponse;
            
        } catch (Exception e) {
            logger.error("Error creating success response", e);
            return createFallbackResponse(messageId, "information", "Operation completed successfully");
        }
    }
    
    public String createErrorResponse(String messageId, String errorMessage) {
        try {
            ObjectNode response = objectMapper.createObjectNode();
            response.put("resourceType", "OperationOutcome");
            response.put("id", messageId != null ? messageId : "unknown");
            
            ArrayNode issues = objectMapper.createArrayNode();
            ObjectNode issue = objectMapper.createObjectNode();
            issue.put("severity", "error");
            
            ObjectNode details = objectMapper.createObjectNode();
            details.put("text", errorMessage);
            issue.set("details", details);
            
            issues.add(issue);
            response.set("issue", issues);
            
            String jsonResponse = objectMapper.writeValueAsString(response);
            logger.debug("Created error response: {}", jsonResponse);
            return jsonResponse;
            
        } catch (Exception e) {
            logger.error("Error creating error response", e);
            return createFallbackResponse(messageId, "error", errorMessage);
        }
    }
    
    public String createWarningResponse(String messageId, String warningMessage) {
        try {
            ObjectNode response = objectMapper.createObjectNode();
            response.put("resourceType", "OperationOutcome");
            response.put("id", messageId != null ? messageId : "unknown");
            
            ArrayNode issues = objectMapper.createArrayNode();
            ObjectNode issue = objectMapper.createObjectNode();
            issue.put("severity", "warning");
            
            ObjectNode details = objectMapper.createObjectNode();
            details.put("text", warningMessage);
            issue.set("details", details);
            
            issues.add(issue);
            response.set("issue", issues);
            
            String jsonResponse = objectMapper.writeValueAsString(response);
            logger.debug("Created warning response: {}", jsonResponse);
            return jsonResponse;
            
        } catch (Exception e) {
            logger.error("Error creating warning response", e);
            return createFallbackResponse(messageId, "warning", warningMessage);
        }
    }
    
    private String createFallbackResponse(String messageId, String severity, String message) {
        // Fallback JSON response if ObjectMapper fails
        return String.format(
            "{\"resourceType\":\"OperationOutcome\",\"id\":\"%s\",\"issue\":[{\"severity\":\"%s\",\"details\":{\"text\":\"%s\"}}]}",
            messageId != null ? messageId : "unknown",
            severity,
            message.replace("\"", "\\\"")
        );
    }
    
    // Common error messages based on the specification
    public String createDeviceNotFoundError(String messageId, String deviceId) {
        return createErrorResponse(messageId, String.format("Cannot find device by UUID `%s'", deviceId));
    }
    
    public String createPatientNotFoundError(String messageId) {
        return createErrorResponse(messageId, "Cannot locate patient");
    }
    
    public String createInvalidDataError(String messageId, String dataType) {
        return createErrorResponse(messageId, String.format("All %s entries were invalid. Check the coding.", dataType));
    }
    
    public String createNoOperationError(String messageId) {
        return createErrorResponse(messageId, "No operation to consume");
    }
    
    public String createUnhandledEventError(String messageId, String eventData) {
        return createErrorResponse(messageId, String.format("Unhandled event `%s'", eventData));
    }
}
