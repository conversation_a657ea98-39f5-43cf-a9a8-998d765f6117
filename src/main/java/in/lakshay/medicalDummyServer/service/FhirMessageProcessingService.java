package in.lakshay.medicalDummyServer.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.lakshay.medicalDummyServer.entity.*;
import in.lakshay.medicalDummyServer.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class FhirMessageProcessingService {
    
    private static final Logger logger = LoggerFactory.getLogger(FhirMessageProcessingService.class);
    
    @Autowired
    private FhirObservationRepository observationRepository;
    
    @Autowired
    private ObservationComponentRepository componentRepository;
    
    @Autowired
    private FhirCommunicationRepository communicationRepository;
    
    @Autowired
    private CommunicationPayloadRepository payloadRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private DeviceRepository deviceRepository;
    
    @Autowired
    private OperationOutcomeService operationOutcomeService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public String processObservation(String jsonPayload, String accountNumber, String deviceId) {
        try {
            JsonNode root = objectMapper.readTree(jsonPayload);
            String observationId = root.get("id").asText();
            
            logger.info("Processing Observation with ID: {}", observationId);
            
            // Check if observation already exists
            if (observationRepository.existsByObservationId(observationId)) {
                logger.warn("Observation with ID {} already exists", observationId);
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Observation with this ID already exists");
            }
            
            // Validate required fields
            if (!root.has("subject") || !root.has("device") || !root.has("component")) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Missing required fields: subject, device, or component");
            }
            
            // Extract patient reference
            String patientReference = root.get("subject").get("reference").asText();
            if (!validatePatientExists(patientReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Cannot locate patient");
            }
            
            // Extract device reference
            String deviceReference = root.get("device").get("reference").asText();
            if (!validateDeviceExists(deviceReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Cannot find device by UUID");
            }
            
            // Determine observation type based on category and components
            String observationType = determineObservationType(root);
            
            // Create and save observation
            FhirObservation observation = createObservation(root, observationId, accountNumber, 
                patientReference, deviceReference, observationType, jsonPayload);
            observation = observationRepository.save(observation);
            
            // Process components
            List<ObservationComponent> components = processComponents(root.get("component"), observation);
            if (components.isEmpty()) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "All " + (observationType.equals("WAVEFORM") ? "waveform" : "vitals") + 
                    " entries were invalid. Check the coding.");
            }
            
            componentRepository.saveAll(components);
            
            // Generate success response based on observation type
            String successMessage = generateSuccessMessage(observationType, components);
            return operationOutcomeService.createSuccessResponse(observationId, successMessage);
            
        } catch (Exception e) {
            logger.error("Error processing observation", e);
            return operationOutcomeService.createErrorResponse(null,
                "Error processing observation: " + e.getMessage());
        }
    }

    public String processCommunication(String jsonPayload, String accountNumber, String deviceId) {
        try {
            JsonNode root = objectMapper.readTree(jsonPayload);
            String communicationId = root.get("id").asText();

            logger.info("Processing Communication with ID: {}", communicationId);

            // Check if communication already exists
            if (communicationRepository.existsByCommunicationId(communicationId)) {
                logger.warn("Communication with ID {} already exists", communicationId);
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Communication with this ID already exists");
            }

            // Validate required fields
            if (!root.has("subject") || !root.has("sender") || !root.has("payload")) {
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Missing required fields: subject, sender, or payload");
            }

            // Extract patient reference
            String patientReference = root.get("subject").get("reference").asText();
            if (!validatePatientExists(patientReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Cannot locate patient");
            }

            // Extract device reference
            String senderReference = root.get("sender").get("reference").asText();
            if (!validateDeviceExists(senderReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Cannot locate device");
            }

            // Create and save communication
            FhirCommunication communication = createCommunication(root, communicationId,
                accountNumber, patientReference, senderReference, jsonPayload);
            communication = communicationRepository.save(communication);

            // Process payload elements
            List<CommunicationPayload> payloads = processPayloads(root.get("payload"), communication);
            payloadRepository.saveAll(payloads);

            return operationOutcomeService.createSuccessResponse(communicationId,
                "Create alarm queued successfully");

        } catch (Exception e) {
            logger.error("Error processing communication", e);
            return operationOutcomeService.createErrorResponse(null,
                "Error processing communication: " + e.getMessage());
        }
    }

    private boolean validatePatientExists(String patientReference, String accountNumber) {
        // For demo purposes, we'll create patient if it doesn't exist
        if (!patientRepository.existsByPatientReferenceAndAccountNumberAndIsActive(
                patientReference, accountNumber, true)) {

            Patient patient = new Patient();
            patient.setPatientReference(patientReference);
            patient.setDisplayName("Demo Patient");
            patient.setAccountNumber(accountNumber);
            patient.setIsActive(true);
            patientRepository.save(patient);

            logger.info("Created new patient: {}", patientReference);
        }
        return true;
    }

    private boolean validateDeviceExists(String deviceReference, String accountNumber) {
        // For demo purposes, we'll create device if it doesn't exist
        if (!deviceRepository.existsByDeviceReferenceAndAccountNumberAndIsActive(
                deviceReference, accountNumber, true)) {

            Device device = new Device();
            device.setDeviceReference(deviceReference);
            device.setDisplayName("Demo Device");
            device.setDeviceUuid(java.util.UUID.randomUUID().toString());
            device.setAccountNumber(accountNumber);
            device.setIsActive(true);
            deviceRepository.save(device);

            logger.info("Created new device: {}", deviceReference);
        }
        return true;
    }

    private String determineObservationType(JsonNode root) {
        // Check category to determine type
        if (root.has("category")) {
            JsonNode category = root.get("category").get(0);
            String categoryCode = category.get("coding").get(0).get("code").asText();

            if ("procedure".equals(categoryCode)) {
                return "WAVEFORM";
            } else if ("vital-signs".equals(categoryCode)) {
                // Check if it has effectivePeriod (alarm) or just effectiveDateTime (numeric)
                if (root.has("effectivePeriod")) {
                    return "ALARM";
                } else {
                    return "NUMERIC";
                }
            }
        }

        // Default to numeric if unclear
        return "NUMERIC";
    }

    private FhirObservation createObservation(JsonNode root, String observationId, String accountNumber,
                                              String patientReference, String deviceReference,
                                              String observationType, String rawData) {
        FhirObservation observation = new FhirObservation();
        observation.setObservationId(observationId);
        observation.setResourceType("Observation");
        observation.setStatus(root.has("status") ? root.get("status").asText() : "final");

        // Set category
        if (root.has("category")) {
            String categoryCode = root.get("category").get(0).get("coding").get(0).get("code").asText();
            observation.setCategoryCode(categoryCode);
        }

        observation.setObservationType(observationType);
        observation.setPatientReference(patientReference);
        observation.setDeviceReference(deviceReference);
        observation.setAccountNumber(accountNumber);
        observation.setRawData(rawData);

        // Set practitioner if present
        if (root.has("performer") && root.get("performer").isArray() && root.get("performer").size() > 0) {
            observation.setPractitionerReference(root.get("performer").get(0).get("reference").asText());
        }

        // Set effective date/time
        if (root.has("effectiveDateTime")) {
            observation.setEffectiveDateTime(parseDateTime(root.get("effectiveDateTime").asText()));
        }

        // Set effective period for alarms
        if (root.has("effectivePeriod")) {
            JsonNode period = root.get("effectivePeriod");
            if (period.has("start")) {
                observation.setEffectivePeriodStart(parseDateTime(period.get("start").asText()));
            }
            if (period.has("end")) {
                observation.setEffectivePeriodEnd(parseDateTime(period.get("end").asText()));
            }
        }

        return observation;
    }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        try {
            // Handle ISO 8601 format with timezone
            if (dateTimeStr.contains("T")) {
                return LocalDateTime.parse(dateTimeStr.substring(0, 19));
            }
            return LocalDateTime.parse(dateTimeStr);
        } catch (DateTimeParseException e) {
            logger.warn("Failed to parse datetime: {}", dateTimeStr);
            return LocalDateTime.now();
        }
    }

    private List<ObservationComponent> processComponents(JsonNode componentsNode, FhirObservation observation) {
        List<ObservationComponent> components = new ArrayList<>();

        for (JsonNode componentNode : componentsNode) {
            try {
                ObservationComponent component = new ObservationComponent();
                component.setObservation(observation);

                // Extract coding information
                JsonNode coding = componentNode.get("code").get("coding").get(0);
                component.setCodingSystem(coding.get("system").asText());
                component.setCodingCode(coding.get("code").asText());
                if (coding.has("display")) {
                    component.setCodingDisplay(coding.get("display").asText());
                }

                // Process based on component type
                if (componentNode.has("valueQuantity")) {
                    // Numeric value
                    JsonNode valueQuantity = componentNode.get("valueQuantity");
                    component.setValueQuantity(valueQuantity.get("value").asDouble());
                    if (valueQuantity.has("unit")) {
                        component.setValueUnit(valueQuantity.get("unit").asText());
                    }
                    if (valueQuantity.has("system")) {
                        component.setValueSystem(valueQuantity.get("system").asText());
                    }
                    if (valueQuantity.has("code")) {
                        component.setValueCode(valueQuantity.get("code").asText());
                    }
                }

                if (componentNode.has("valueSampledData")) {
                    // Waveform data
                    JsonNode sampledData = componentNode.get("valueSampledData");
                    if (sampledData.has("origin")) {
                        component.setSampledOriginValue(sampledData.get("origin").get("value").asDouble());
                    }
                    if (sampledData.has("period")) {
                        component.setSampledPeriod(sampledData.get("period").asDouble());
                    }
                    if (sampledData.has("factor")) {
                        component.setSampledFactor(sampledData.get("factor").asDouble());
                    }
                    if (sampledData.has("lowerLimit")) {
                        component.setSampledLowerLimit(sampledData.get("lowerLimit").asDouble());
                    }
                    if (sampledData.has("upperLimit")) {
                        component.setSampledUpperLimit(sampledData.get("upperLimit").asDouble());
                    }
                    if (sampledData.has("dimensions")) {
                        component.setSampledDimensions(sampledData.get("dimensions").asInt());
                    }
                    if (sampledData.has("data")) {
                        component.setSampledData(sampledData.get("data").asText());
                    }
                }

                // Process interpretation for alarms
                if (componentNode.has("interpretation")) {
                    JsonNode interpretation = componentNode.get("interpretation").get(0).get("coding").get(0);
                    component.setInterpretationCode(interpretation.get("code").asText());
                    component.setInterpretationDisplay(interpretation.get("display").asText());
                }

                // Process reference ranges for alarms
                if (componentNode.has("referenceRange")) {
                    JsonNode refRange = componentNode.get("referenceRange").get(0);
                    if (refRange.has("high")) {
                        component.setReferenceHighValue(refRange.get("high").get("value").asDouble());
                    }
                    if (refRange.has("low")) {
                        component.setReferenceLowValue(refRange.get("low").get("value").asDouble());
                    }
                    if (refRange.has("high") && refRange.get("high").has("unit")) {
                        component.setReferenceUnit(refRange.get("high").get("unit").asText());
                    }
                }

                components.add(component);

            } catch (Exception e) {
                logger.warn("Failed to process component: {}", e.getMessage());
                // Continue processing other components
            }
        }

        return components;
    }

    private FhirCommunication createCommunication(JsonNode root, String communicationId, String accountNumber,
                                                  String patientReference, String senderReference, String rawData) {
        FhirCommunication communication = new FhirCommunication();
        communication.setCommunicationId(communicationId);
        communication.setResourceType("Communication");
        communication.setStatus(root.get("status").asText());
        communication.setPatientReference(patientReference);
        communication.setSenderReference(senderReference);
        communication.setAccountNumber(accountNumber);
        communication.setRawData(rawData);

        // Parse sent time
        if (root.has("sent")) {
            communication.setSentTime(parseDateTime(root.get("sent").asText()));
        } else {
            communication.setSentTime(LocalDateTime.now());
        }

        return communication;
    }

    private List<CommunicationPayload> processPayloads(JsonNode payloadsNode, FhirCommunication communication) {
        List<CommunicationPayload> payloads = new ArrayList<>();

        for (JsonNode payloadNode : payloadsNode) {
            if (payloadNode.has("contentString")) {
                CommunicationPayload payload = new CommunicationPayload();
                payload.setCommunication(communication);

                String contentString = payloadNode.get("contentString").asText();
                payload.setContentString(contentString);

                // Determine payload type and extract value
                if (contentString.startsWith("Error:") || contentString.startsWith("Resolved:")) {
                    payload.setPayloadType("ERROR_STATUS");
                    payload.setPayloadValue(contentString.substring(contentString.indexOf(":") + 1).trim());
                } else if (contentString.startsWith("Alert Priority:")) {
                    payload.setPayloadType("ALERT_PRIORITY");
                    payload.setPayloadValue(contentString.substring(contentString.indexOf(":") + 1).trim());
                } else if (contentString.startsWith("StartTime:")) {
                    payload.setPayloadType("START_TIME");
                    payload.setPayloadValue(contentString.substring(contentString.indexOf(":") + 1).trim());
                } else if (contentString.startsWith("Type:")) {
                    payload.setPayloadType("TYPE");
                    payload.setPayloadValue(contentString.substring(contentString.indexOf(":") + 1).trim());
                } else if (contentString.startsWith("ID:")) {
                    payload.setPayloadType("ID");
                    payload.setPayloadValue(contentString.substring(contentString.indexOf(":") + 1).trim());
                } else {
                    payload.setPayloadType("OTHER");
                    payload.setPayloadValue(contentString);
                }

                payloads.add(payload);
            }
        }

        return payloads;
    }

    private String generateSuccessMessage(String observationType, List<ObservationComponent> components) {
        switch (observationType) {
            case "WAVEFORM":
                return "Waveform operation queued successfully";
            case "ALARM":
                return "Create alarm queued successfully";
            case "NUMERIC":
                // Try to determine specific vital type from first component
                if (!components.isEmpty()) {
                    String codingCode = components.get(0).getCodingCode();
                    String vitalType = getVitalTypeFromCode(codingCode);
                    return String.format("Create %s vital queued successfully", vitalType);
                }
                return "Create vital queued successfully";
            default:
                return "Operation queued successfully";
        }
    }

    private String getVitalTypeFromCode(String codingCode) {
        // Map common MDC codes to vital types
        switch (codingCode) {
            case "147842": return "HR";
            case "151554": case "9279-1": return "RR";
            case "150316": case "2708-6": return "SpO2";
            case "150364": case "8310-5": return "Temperature";
            case "150017": return "Systolic BP";
            case "150018": return "Diastolic BP";
            case "149530": case "8889-8": return "Pulse Rate";
            default: return "vital";
        }
    }
}
