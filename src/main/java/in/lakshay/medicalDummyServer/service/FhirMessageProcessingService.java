package in.lakshay.medicalDummyServer.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.lakshay.medicalDummyServer.entity.*;
import in.lakshay.medicalDummyServer.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class FhirMessageProcessingService {
    
    private static final Logger logger = LoggerFactory.getLogger(FhirMessageProcessingService.class);
    
    @Autowired
    private FhirObservationRepository observationRepository;
    
    @Autowired
    private ObservationComponentRepository componentRepository;
    
    @Autowired
    private FhirCommunicationRepository communicationRepository;
    
    @Autowired
    private CommunicationPayloadRepository payloadRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private DeviceRepository deviceRepository;
    
    @Autowired
    private OperationOutcomeService operationOutcomeService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public String processObservation(String jsonPayload, String accountNumber, String deviceId) {
        try {
            JsonNode root = objectMapper.readTree(jsonPayload);
            String observationId = root.get("id").asText();
            
            logger.info("Processing Observation with ID: {}", observationId);
            
            // Check if observation already exists
            if (observationRepository.existsByObservationId(observationId)) {
                logger.warn("Observation with ID {} already exists", observationId);
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Observation with this ID already exists");
            }
            
            // Validate required fields
            if (!root.has("subject") || !root.has("device") || !root.has("component")) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Missing required fields: subject, device, or component");
            }
            
            // Extract patient reference
            String patientReference = root.get("subject").get("reference").asText();
            if (!validatePatientExists(patientReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Cannot locate patient");
            }
            
            // Extract device reference
            String deviceReference = root.get("device").get("reference").asText();
            if (!validateDeviceExists(deviceReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "Cannot find device by UUID");
            }
            
            // Determine observation type based on category and components
            String observationType = determineObservationType(root);
            
            // Create and save observation
            FhirObservation observation = createObservation(root, observationId, accountNumber, 
                patientReference, deviceReference, observationType, jsonPayload);
            observation = observationRepository.save(observation);
            
            // Process components
            List<ObservationComponent> components = processComponents(root.get("component"), observation);
            if (components.isEmpty()) {
                return operationOutcomeService.createErrorResponse(observationId, 
                    "All " + (observationType.equals("WAVEFORM") ? "waveform" : "vitals") + 
                    " entries were invalid. Check the coding.");
            }
            
            componentRepository.saveAll(components);
            
            // Generate success response based on observation type
            String successMessage = generateSuccessMessage(observationType, components);
            return operationOutcomeService.createSuccessResponse(observationId, successMessage);
            
        } catch (Exception e) {
            logger.error("Error processing observation", e);
            return operationOutcomeService.createErrorResponse(null,
                "Error processing observation: " + e.getMessage());
        }
    }

    public String processCommunication(String jsonPayload, String accountNumber, String deviceId) {
        try {
            JsonNode root = objectMapper.readTree(jsonPayload);
            String communicationId = root.get("id").asText();

            logger.info("Processing Communication with ID: {}", communicationId);

            // Check if communication already exists
            if (communicationRepository.existsByCommunicationId(communicationId)) {
                logger.warn("Communication with ID {} already exists", communicationId);
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Communication with this ID already exists");
            }

            // Validate required fields
            if (!root.has("subject") || !root.has("sender") || !root.has("payload")) {
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Missing required fields: subject, sender, or payload");
            }

            // Extract patient reference
            String patientReference = root.get("subject").get("reference").asText();
            if (!validatePatientExists(patientReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Cannot locate patient");
            }

            // Extract device reference
            String senderReference = root.get("sender").get("reference").asText();
            if (!validateDeviceExists(senderReference, accountNumber)) {
                return operationOutcomeService.createErrorResponse(communicationId,
                    "Cannot locate device");
            }

            // Create and save communication
            FhirCommunication communication = createCommunication(root, communicationId,
                accountNumber, patientReference, senderReference, jsonPayload);
            communication = communicationRepository.save(communication);

            // Process payload elements
            List<CommunicationPayload> payloads = processPayloads(root.get("payload"), communication);
            payloadRepository.saveAll(payloads);

            return operationOutcomeService.createSuccessResponse(communicationId,
                "Create alarm queued successfully");

        } catch (Exception e) {
            logger.error("Error processing communication", e);
            return operationOutcomeService.createErrorResponse(null,
                "Error processing communication: " + e.getMessage());
        }
    }

    private boolean validatePatientExists(String patientReference, String accountNumber) {
        // For demo purposes, we'll create patient if it doesn't exist
        if (!patientRepository.existsByPatientReferenceAndAccountNumberAndIsActive(
                patientReference, accountNumber, true)) {

            Patient patient = new Patient();
            patient.setPatientReference(patientReference);
            patient.setDisplayName("Demo Patient");
            patient.setAccountNumber(accountNumber);
            patient.setIsActive(true);
            patientRepository.save(patient);

            logger.info("Created new patient: {}", patientReference);
        }
        return true;
    }

    private boolean validateDeviceExists(String deviceReference, String accountNumber) {
        // For demo purposes, we'll create device if it doesn't exist
        if (!deviceRepository.existsByDeviceReferenceAndAccountNumberAndIsActive(
                deviceReference, accountNumber, true)) {

            Device device = new Device();
            device.setDeviceReference(deviceReference);
            device.setDisplayName("Demo Device");
            device.setDeviceUuid(java.util.UUID.randomUUID().toString());
            device.setAccountNumber(accountNumber);
            device.setIsActive(true);
            deviceRepository.save(device);

            logger.info("Created new device: {}", deviceReference);
        }
        return true;
    }

    private String determineObservationType(JsonNode root) {
        // Check category to determine type
        if (root.has("category")) {
            JsonNode category = root.get("category").get(0);
            String categoryCode = category.get("coding").get(0).get("code").asText();

            if ("procedure".equals(categoryCode)) {
                return "WAVEFORM";
            } else if ("vital-signs".equals(categoryCode)) {
                // Check if it has effectivePeriod (alarm) or just effectiveDateTime (numeric)
                if (root.has("effectivePeriod")) {
                    return "ALARM";
                } else {
                    return "NUMERIC";
                }
            }
        }

        // Default to numeric if unclear
        return "NUMERIC";
    }

    private FhirObservation createObservation(JsonNode root, String observationId, String accountNumber,
                                              String patientReference, String deviceReference,
                                              String observationType, String rawData) {
        FhirObservation observation = new FhirObservation();
        observation.setObservationId(observationId);
        observation.setResourceType("Observation");
        observation.setStatus(root.has("status") ? root.get("status").asText() : "final");

        // Set category
        if (root.has("category")) {
            String categoryCode = root.get("category").get(0).get("coding").get(0).get("code").asText();
            observation.setCategoryCode(categoryCode);
        }

        observation.setObservationType(observationType);
        observation.setPatientReference(patientReference);
        observation.setDeviceReference(deviceReference);
        observation.setAccountNumber(accountNumber);
        observation.setRawData(rawData);

        // Set practitioner if present
        if (root.has("performer") && root.get("performer").isArray() && root.get("performer").size() > 0) {
            observation.setPractitionerReference(root.get("performer").get(0).get("reference").asText());
        }

        // Set effective date/time
        if (root.has("effectiveDateTime")) {
            observation.setEffectiveDateTime(parseDateTime(root.get("effectiveDateTime").asText()));
        }

        // Set effective period for alarms
        if (root.has("effectivePeriod")) {
            JsonNode period = root.get("effectivePeriod");
            if (period.has("start")) {
                observation.setEffectivePeriodStart(parseDateTime(period.get("start").asText()));
            }
            if (period.has("end")) {
                observation.setEffectivePeriodEnd(parseDateTime(period.get("end").asText()));
            }
        }

        return observation;
    }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        try {
            // Handle ISO 8601 format with timezone
            if (dateTimeStr.contains("T")) {
                return LocalDateTime.parse(dateTimeStr.substring(0, 19));
            }
            return LocalDateTime.parse(dateTimeStr);
        } catch (DateTimeParseException e) {
            logger.warn("Failed to parse datetime: {}", dateTimeStr);
            return LocalDateTime.now();
        }
    }
