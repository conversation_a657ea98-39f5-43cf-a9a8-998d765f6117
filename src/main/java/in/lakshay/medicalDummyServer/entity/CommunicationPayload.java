package in.lakshay.medicalDummyServer.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Entity
@Table(name = "communication_payloads")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommunicationPayload {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "communication_id", nullable = false)
    private FhirCommunication communication;
    
    @Column(name = "content_string", nullable = false)
    private String contentString; // e.g., "Error: OutOfRange", "Alert Priority: LOW", etc.
    
    @Column(name = "payload_type")
    private String payloadType; // "ERROR_STATUS", "ALERT_PRIORITY", "START_TIME", "TYPE", "ID"
    
    @Column(name = "payload_value")
    private String payloadValue; // Extracted value from contentString
}
