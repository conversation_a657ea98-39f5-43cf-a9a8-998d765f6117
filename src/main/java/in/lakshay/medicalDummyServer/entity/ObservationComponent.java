package in.lakshay.medicalDummyServer.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Entity
@Table(name = "observation_components")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ObservationComponent {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "observation_id", nullable = false)
    private FhirObservation observation;
    
    // Coding information
    @Column(name = "coding_system", nullable = false)
    private String codingSystem; // "urn:oid:2.16.840.1.113883.6.24" or "http://loinc.org"
    
    @Column(name = "coding_code", nullable = false)
    private String codingCode; // MDC or LOINC code
    
    @Column(name = "coding_display")
    private String codingDisplay; // Human readable display
    
    // Value information - for numerics
    @Column(name = "value_quantity")
    private Double valueQuantity;
    
    @Column(name = "value_unit")
    private String valueUnit;
    
    @Column(name = "value_system")
    private String valueSystem;
    
    @Column(name = "value_code")
    private String valueCode;
    
    // Sampled data - for waveforms
    @Column(name = "sampled_origin_value")
    private Double sampledOriginValue;
    
    @Column(name = "sampled_period")
    private Double sampledPeriod;
    
    @Column(name = "sampled_factor")
    private Double sampledFactor;
    
    @Column(name = "sampled_lower_limit")
    private Double sampledLowerLimit;
    
    @Column(name = "sampled_upper_limit")
    private Double sampledUpperLimit;
    
    @Column(name = "sampled_dimensions")
    private Integer sampledDimensions;
    
    @Column(name = "sampled_data", columnDefinition = "TEXT")
    private String sampledData; // Space-delimited values
    
    // Interpretation - for alarms
    @Column(name = "interpretation_code")
    private String interpretationCode; // "L", "N", "H"
    
    @Column(name = "interpretation_display")
    private String interpretationDisplay; // "Low", "Normal", "High"
    
    // Reference ranges - for alarms
    @Column(name = "reference_high_value")
    private Double referenceHighValue;
    
    @Column(name = "reference_low_value")
    private Double referenceLowValue;
    
    @Column(name = "reference_unit")
    private String referenceUnit;
}
