package in.lakshay.medicalDummyServer.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "fhir_observations")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FhirObservation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "observation_id", nullable = false, unique = true)
    private String observationId; // UUID from FHIR message
    
    @Column(name = "resource_type", nullable = false)
    private String resourceType = "Observation";
    
    @Column(name = "status", nullable = false)
    private String status = "final";
    
    @Column(name = "category_code", nullable = false)
    private String categoryCode; // "procedure", "vital-signs"
    
    @Column(name = "observation_type", nullable = false)
    private String observationType; // "WAVEFORM", "NUMERIC", "ALARM"
    
    @Column(name = "patient_reference", nullable = false)
    private String patientReference;
    
    @Column(name = "device_reference", nullable = false)
    private String deviceReference;
    
    @Column(name = "practitioner_reference")
    private String practitionerReference;
    
    @Column(name = "effective_date_time")
    private LocalDateTime effectiveDateTime;
    
    @Column(name = "effective_period_start")
    private LocalDateTime effectivePeriodStart;
    
    @Column(name = "effective_period_end")
    private LocalDateTime effectivePeriodEnd;
    
    @Column(name = "account_number", nullable = false, length = 4)
    private String accountNumber;
    
    @Column(name = "raw_data", columnDefinition = "TEXT")
    private String rawData; // Store original JSON for reference
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
