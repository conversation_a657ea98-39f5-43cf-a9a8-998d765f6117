package in.lakshay.medicalDummyServer.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "fhir_communications")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FhirCommunication {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "communication_id", nullable = false, unique = true)
    private String communicationId; // UUID from FHIR message
    
    @Column(name = "resource_type", nullable = false)
    private String resourceType = "Communication";
    
    @Column(name = "status", nullable = false)
    private String status; // "inprogress", "completed"
    
    @Column(name = "category_code", nullable = false)
    private String categoryCode = "alert";
    
    @Column(name = "patient_reference", nullable = false)
    private String patientReference;
    
    @Column(name = "sender_reference", nullable = false)
    private String senderReference; // Device reference
    
    @Column(name = "sent_time", nullable = false)
    private LocalDateTime sentTime;
    
    @Column(name = "account_number", nullable = false, length = 4)
    private String accountNumber;
    
    @Column(name = "raw_data", columnDefinition = "TEXT")
    private String rawData; // Store original JSON for reference
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
