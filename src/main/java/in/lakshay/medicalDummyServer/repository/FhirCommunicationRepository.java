package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.FhirCommunication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface FhirCommunicationRepository extends JpaRepository<FhirCommunication, Long> {
    
    Optional<FhirCommunication> findByCommunicationId(String communicationId);
    
    List<FhirCommunication> findByPatientReferenceAndAccountNumber(
        String patientReference, String accountNumber);
    
    List<FhirCommunication> findBySenderReferenceAndAccountNumber(
        String senderReference, String accountNumber);
    
    List<FhirCommunication> findByStatusAndAccountNumber(
        String status, String accountNumber);
    
    @Query("SELECT c FROM FhirCommunication c WHERE c.accountNumber = :accountNumber " +
           "AND c.sentTime BETWEEN :startTime AND :endTime")
    List<FhirCommunication> findByAccountNumberAndSentTimeBetween(
        @Param("accountNumber") String accountNumber,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT c FROM FhirCommunication c WHERE c.patientReference = :patientReference " +
           "AND c.accountNumber = :accountNumber AND c.status = :status " +
           "ORDER BY c.sentTime DESC")
    List<FhirCommunication> findByPatientAndStatusOrderByTimeDesc(
        @Param("patientReference") String patientReference,
        @Param("accountNumber") String accountNumber,
        @Param("status") String status);
    
    boolean existsByCommunicationId(String communicationId);
}
