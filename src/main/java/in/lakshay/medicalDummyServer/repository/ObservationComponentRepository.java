package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.ObservationComponent;
import in.lakshay.medicalDummyServer.entity.FhirObservation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ObservationComponentRepository extends JpaRepository<ObservationComponent, Long> {
    
    List<ObservationComponent> findByObservation(FhirObservation observation);
    
    List<ObservationComponent> findByObservationId(Long observationId);
    
    List<ObservationComponent> findByCodingSystemAndCodingCode(
        String codingSystem, String codingCode);
    
    @Query("SELECT oc FROM ObservationComponent oc WHERE oc.observation.observationId = :observationId")
    List<ObservationComponent> findByObservationObservationId(@Param("observationId") String observationId);
    
    @Query("SELECT oc FROM ObservationComponent oc WHERE oc.observation.accountNumber = :accountNumber " +
           "AND oc.codingSystem = :codingSystem AND oc.codingCode = :codingCode")
    List<ObservationComponent> findByAccountNumberAndCoding(
        @Param("accountNumber") String accountNumber,
        @Param("codingSystem") String codingSystem,
        @Param("codingCode") String codingCode);
    
    @Query("SELECT oc FROM ObservationComponent oc WHERE oc.observation.patientReference = :patientReference " +
           "AND oc.observation.accountNumber = :accountNumber AND oc.codingCode = :codingCode")
    List<ObservationComponent> findByPatientAndCodingCode(
        @Param("patientReference") String patientReference,
        @Param("accountNumber") String accountNumber,
        @Param("codingCode") String codingCode);
    
    void deleteByObservation(FhirObservation observation);
}
