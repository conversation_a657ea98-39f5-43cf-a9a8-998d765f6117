package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.CommunicationPayload;
import in.lakshay.medicalDummyServer.entity.FhirCommunication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CommunicationPayloadRepository extends JpaRepository<CommunicationPayload, Long> {
    
    List<CommunicationPayload> findByCommunication(FhirCommunication communication);
    
    List<CommunicationPayload> findByCommunicationId(Long communicationId);
    
    List<CommunicationPayload> findByPayloadType(String payloadType);
    
    Optional<CommunicationPayload> findByCommunicationAndPayloadType(
        FhirCommunication communication, String payloadType);
    
    @Query("SELECT cp FROM CommunicationPayload cp WHERE cp.communication.communicationId = :communicationId")
    List<CommunicationPayload> findByCommunicationCommunicationId(@Param("communicationId") String communicationId);
    
    @Query("SELECT cp FROM CommunicationPayload cp WHERE cp.communication.accountNumber = :accountNumber " +
           "AND cp.payloadType = :payloadType")
    List<CommunicationPayload> findByAccountNumberAndPayloadType(
        @Param("accountNumber") String accountNumber,
        @Param("payloadType") String payloadType);
    
    void deleteByCommunication(FhirCommunication communication);
}
