package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.FhirObservation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface FhirObservationRepository extends JpaRepository<FhirObservation, Long> {
    
    Optional<FhirObservation> findByObservationId(String observationId);
    
    List<FhirObservation> findByPatientReferenceAndAccountNumber(
        String patientReference, String accountNumber);
    
    List<FhirObservation> findByDeviceReferenceAndAccountNumber(
        String deviceReference, String accountNumber);
    
    List<FhirObservation> findByObservationTypeAndAccountNumber(
        String observationType, String accountNumber);
    
    @Query("SELECT o FROM FhirObservation o WHERE o.accountNumber = :accountNumber " +
           "AND o.effectiveDateTime BETWEEN :startTime AND :endTime")
    List<FhirObservation> findByAccountNumberAndEffectiveDateTimeBetween(
        @Param("accountNumber") String accountNumber,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT o FROM FhirObservation o WHERE o.patientReference = :patientReference " +
           "AND o.accountNumber = :accountNumber AND o.observationType = :observationType " +
           "ORDER BY o.effectiveDateTime DESC")
    List<FhirObservation> findByPatientAndTypeOrderByDateDesc(
        @Param("patientReference") String patientReference,
        @Param("accountNumber") String accountNumber,
        @Param("observationType") String observationType);
    
    boolean existsByObservationId(String observationId);
}
