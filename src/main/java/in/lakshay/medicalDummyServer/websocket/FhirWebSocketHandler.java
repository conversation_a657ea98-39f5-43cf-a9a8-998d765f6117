package in.lakshay.medicalDummyServer.websocket;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.lakshay.medicalDummyServer.service.FhirMessageProcessingService;
import in.lakshay.medicalDummyServer.service.OperationOutcomeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.util.concurrent.ConcurrentHashMap;

@Component
public class FhirWebSocketHandler implements WebSocketHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(FhirWebSocketHandler.class);
    
    @Autowired
    private FhirMessageProcessingService fhirMessageProcessingService;
    
    @Autowired
    private OperationOutcomeService operationOutcomeService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        String sessionKey = accountNumber + ":" + deviceId;
        
        sessions.put(sessionKey, session);
        logger.info("WebSocket connection established for account: {} and device: {}", accountNumber, deviceId);
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        
        logger.info("Received WebSocket message from account: {} and device: {}", accountNumber, deviceId);
        
        try {
            String payload = message.getPayload().toString();
            logger.debug("Message payload: {}", payload);
            
            // Parse JSON message
            JsonNode jsonNode = objectMapper.readTree(payload);
            String resourceType = jsonNode.get("resourceType").asText();
            String messageId = jsonNode.has("id") ? jsonNode.get("id").asText() : null;
            
            String responseJson;
            
            switch (resourceType) {
                case "Observation":
                    responseJson = fhirMessageProcessingService.processObservation(payload, accountNumber, deviceId);
                    break;
                case "Communication":
                    responseJson = fhirMessageProcessingService.processCommunication(payload, accountNumber, deviceId);
                    break;
                default:
                    logger.warn("Unhandled resource type: {}", resourceType);
                    responseJson = operationOutcomeService.createErrorResponse(
                        messageId, String.format("Unhandled event `%s'", payload));
                    break;
            }
            
            // Send response back to client
            session.sendMessage(new TextMessage(responseJson));
            logger.info("Sent response for message ID: {}", messageId);
            
        } catch (Exception e) {
            logger.error("Error processing WebSocket message", e);
            String errorResponse = operationOutcomeService.createErrorResponse(
                null, "Error processing message: " + e.getMessage());
            session.sendMessage(new TextMessage(errorResponse));
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        
        logger.error("WebSocket transport error for account: {} and device: {}", accountNumber, deviceId, exception);
        
        // Close session on transport error
        if (session.isOpen()) {
            session.close(CloseStatus.SERVER_ERROR);
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        String sessionKey = accountNumber + ":" + deviceId;
        
        sessions.remove(sessionKey);
        logger.info("WebSocket connection closed for account: {} and device: {}. Status: {}", 
                   accountNumber, deviceId, closeStatus);
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    // Utility method to get active session for account/device
    public WebSocketSession getSession(String accountNumber, String deviceId) {
        return sessions.get(accountNumber + ":" + deviceId);
    }
    
    // Utility method to send message to specific session
    public void sendMessageToSession(String accountNumber, String deviceId, String message) {
        WebSocketSession session = getSession(accountNumber, deviceId);
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
                logger.info("Sent message to account: {} and device: {}", accountNumber, deviceId);
            } catch (Exception e) {
                logger.error("Error sending message to WebSocket session", e);
            }
        } else {
            logger.warn("No active session found for account: {} and device: {}", accountNumber, deviceId);
        }
    }
}
