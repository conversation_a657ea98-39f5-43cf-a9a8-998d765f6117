spring.application.name=medicalDummyServer

# Server Configuration
server.port=8080

# Database Configuration (H2 for development)
spring.datasource.url=jdbc:h2:mem:medical_dummy_db
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console (for development)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# WebSocket Configuration
websocket.allowed-origins=*

# Logging Configuration
logging.level.in.lakshay.medicalDummyServer=DEBUG
logging.level.org.springframework.web.socket=DEBUG
logging.level.org.springframework.security=DEBUG

# FHIR Configuration
fhir.base-url=https://rpm.510.test.safensound.io
fhir.version=1
fhir.name=FHIR
