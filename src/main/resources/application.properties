spring.application.name=medicalDummyServer

# Server Configuration
server.port=8080

# Database Configuration (PostgreSQL)
spring.datasource.url=*************************************************
spring.datasource.username=postgres
spring.datasource.password=password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# WebSocket Configuration
websocket.allowed-origins=*

# Logging Configuration
logging.level.in.lakshay.medicalDummyServer=DEBUG
logging.level.org.springframework.web.socket=DEBUG
logging.level.org.springframework.security=DEBUG

# FHIR Configuration
fhir.base-url=https://rpm.510.test.safensound.io
fhir.version=1
fhir.name=FHIR
