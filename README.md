# Medical Dummy Server

A simple Spring Boot-based mock server for testing Android applications locally. This server accepts any JSON data via REST API and WebSocket endpoints, stores it in PostgreSQL, and returns basic success/error responses.

## Features

- **Simple JSON Storage**: Accepts and stores any JSON data without validation
- **REST API Endpoints**: Device association, JWT token generation, and data storage
- **WebSocket Support**: Real-time JSON message storage
- **PostgreSQL Database**: Persistent data storage
- **JWT Authentication**: Basic token-based authentication for testing
- **Data Retrieval**: Simple endpoints to view stored data for verification

## API Endpoints

### 1. Device Association
```
GET /api/v1/associate/{account}/{deviceId}
```
Associates a device with an organization account.

**Example:**
```bash
curl -X GET "http://localhost:8080/api/v1/associate/1234/************"
```

**Response:**
```json
{
  "name": "FHIR",
  "version": "1",
  "payload": {
    "authToken": "base64-encoded-token",
    "loginUrl": "http://localhost:8080",
    "baseUri": "/oauth/token",
    "accountNumber": "1234"
  }
}
```

### 2. JWT Token Generation
```
POST /oauth/token
```
Generates JWT access and refresh tokens.

**Example:**
```bash
curl -X POST "http://localhost:8080/oauth/token?accountNumber=1234&username=hubapi&Origin=EMR&Token=sample" \
  -H "Authorization: Basic base64-encoded-credentials"
```

**Response:**
```json
{
  "role": "HUB",
  "liveViewOnly": true,
  "mustChangePassword": "false",
  "themeMode": "LIGHT",
  "token_type": "Bearer",
  "accountNumber": "1234",
  "userName": "Hub Api User",
  "userId": 20950,
  "access_token": "jwt-access-token",
  "organizationId": 768,
  "refresh_token": "jwt-refresh-token",
  "scope": "HUB HUB_API",
  "pinLength": 6,
  "expires_in": 86398,
  "passcode": "null"
}
```

### 3. Data Storage Endpoint
```
POST /api/data/{account}/{deviceId}
```
Accepts any JSON data and stores it in the database.

**Example:**
```bash
curl -X POST "http://localhost:8080/api/data/1234/device001" \
  -H "Content-Type: application/json" \
  -d '{"heartRate": 72, "timestamp": "2025-01-09T10:30:00"}'
```

### 4. WebSocket Data Endpoint
```
WebSocket: /api/v1/fhir/{account}/{deviceId}/store
```
Real-time JSON message storage via WebSocket.

**Connection:**
```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/fhir/1234/************/store', [], {
  headers: {
    'Authorization': 'Bearer jwt-access-token'
  }
});
```

## Data Retrieval Endpoints

### Get All Messages
```
GET /api/data/messages
```

### Get Messages by Account
```
GET /api/data/messages/account/{account}
```

### Get Messages by Account and Device
```
GET /api/data/messages/account/{account}/device/{deviceId}
```

### Get Statistics
```
GET /api/data/statistics
```

## Setup Instructions

### Prerequisites
- Java 17 or higher
- PostgreSQL database
- Maven 3.6+

### Database Setup
1. Create a PostgreSQL database:
```sql
CREATE DATABASE medical_dummy_db;
```

2. Update `src/main/resources/application.properties`:
```properties
spring.datasource.url=*************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### Running the Application
1. Clone the repository
2. Configure the database connection in `application.properties`
3. Run the application:
```bash
./mvnw spring-boot:run
```

The server will start on `http://localhost:8080`

### Testing
Run the tests:
```bash
./mvnw test
```

## Test Endpoints

### Health Check
```
GET /api/test/health
```

### Sample Data
```
GET /api/test/sample-observation
GET /api/test/sample-communication
```

### Data Retrieval
```
GET /api/test/observations
GET /api/test/observations/account/{accountNumber}
GET /api/test/communications
```

## Sample Data

The application automatically creates sample data on startup:
- Device associations for accounts 1234, 2000, 5678
- Sample patients and devices
- Ready-to-use test data for development

## Configuration

Key configuration properties in `application.properties`:

```properties
# Server
server.port=8080

# Database
spring.datasource.url=*************************************************
spring.datasource.username=postgres
spring.datasource.password=password

# JWT
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=********

# WebSocket
websocket.allowed-origins=*

# FHIR
fhir.base-url=https://rpm.510.test.safensound.io
```

## Architecture

- **Controllers**: Handle HTTP requests and WebSocket connections
- **Services**: Business logic for FHIR message processing and JWT handling
- **Repositories**: Data access layer using Spring Data JPA
- **Entities**: JPA entities for database mapping
- **DTOs**: Data transfer objects for API responses

## Error Handling

The server returns FHIR-compliant OperationOutcome responses for errors:

```json
{
  "resourceType": "OperationOutcome",
  "id": "message-id",
  "issue": [{
    "severity": "error",
    "details": {
      "text": "Error description"
    }
  }]
}
```

## Contributing

1. Follow the existing code style (single-line comments only)
2. Add tests for new functionality
3. Update documentation as needed

## License

This project is for educational and development purposes.
