# Medical Dummy Server

A Spring Boot-based REST API server with WebSocket support for handling medical data in FHIR format. This server is designed to receive medical data from Kotlin Android applications and store it in a PostgreSQL database.

## Features

- **REST API Endpoints**: Device association and JWT token generation
- **WebSocket Support**: Real-time FHIR message processing
- **FHIR Compliance**: Supports Observation and Communication resources
- **Database Integration**: PostgreSQL with JPA/Hibernate
- **JWT Authentication**: Secure token-based authentication
- **Medical Data Types**: Waveforms, Numerics (vitals), Limit Alarms, and Technical Alarms

## API Endpoints

### 1. Device Association
```
GET /api/v1/associate/{account}/{deviceId}
```
Associates a device with an organization account.

**Example:**
```bash
curl -X GET "http://localhost:8080/api/v1/associate/1234/************"
```

**Response:**
```json
{
  "name": "FHIR",
  "version": "1",
  "payload": {
    "authToken": "base64-encoded-token",
    "loginUrl": "https://rpm.510.test.safensound.io",
    "baseUri": "/oauth/token?accountNumber=1234&username=hubapi&Origin=EMR&Token=...",
    "accountNumber": "1234"
  }
}
```

### 2. JWT Token Generation
```
POST /oauth/token
```
Generates JWT access and refresh tokens.

**Example:**
```bash
curl -X POST "http://localhost:8080/oauth/token?accountNumber=1234&username=hubapi&Origin=EMR&Token=sample" \
  -H "Authorization: Basic base64-encoded-credentials"
```

**Response:**
```json
{
  "role": "HUB",
  "liveViewOnly": true,
  "mustChangePassword": "false",
  "themeMode": "LIGHT",
  "token_type": "Bearer",
  "accountNumber": "1234",
  "userName": "Hub Api User",
  "userId": 20950,
  "access_token": "jwt-access-token",
  "organizationId": 768,
  "refresh_token": "jwt-refresh-token",
  "scope": "HUB HUB_API",
  "pinLength": 6,
  "expires_in": 86398,
  "passcode": "null"
}
```

### 3. WebSocket FHIR Endpoint
```
WebSocket: /api/v1/fhir/{account}/{deviceId}/store
```
Real-time bidirectional FHIR message processing.

**Connection:**
```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/fhir/1234/************/store', [], {
  headers: {
    'Authorization': 'Bearer jwt-access-token'
  }
});
```

## FHIR Message Types

### 1. Observation (Waveforms)
For ECG, SpO2, and other waveform data.

### 2. Observation (Numerics)
For vital signs like heart rate, blood pressure, temperature.

### 3. Observation (Limit Alarms)
For alarm conditions when vitals exceed thresholds.

### 4. Communication (Technical Alarms)
For device-related alerts and status messages.

## Setup Instructions

### Prerequisites
- Java 17 or higher
- PostgreSQL database
- Maven 3.6+

### Database Setup
1. Create a PostgreSQL database:
```sql
CREATE DATABASE medical_dummy_db;
```

2. Update `src/main/resources/application.properties`:
```properties
spring.datasource.url=*************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### Running the Application
1. Clone the repository
2. Configure the database connection in `application.properties`
3. Run the application:
```bash
./mvnw spring-boot:run
```

The server will start on `http://localhost:8080`

### Testing
Run the tests:
```bash
./mvnw test
```

## Test Endpoints

### Health Check
```
GET /api/test/health
```

### Sample Data
```
GET /api/test/sample-observation
GET /api/test/sample-communication
```

### Data Retrieval
```
GET /api/test/observations
GET /api/test/observations/account/{accountNumber}
GET /api/test/communications
```

## Sample Data

The application automatically creates sample data on startup:
- Device associations for accounts 1234, 2000, 5678
- Sample patients and devices
- Ready-to-use test data for development

## Configuration

Key configuration properties in `application.properties`:

```properties
# Server
server.port=8080

# Database
spring.datasource.url=*************************************************
spring.datasource.username=postgres
spring.datasource.password=password

# JWT
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=********

# WebSocket
websocket.allowed-origins=*

# FHIR
fhir.base-url=https://rpm.510.test.safensound.io
```

## Architecture

- **Controllers**: Handle HTTP requests and WebSocket connections
- **Services**: Business logic for FHIR message processing and JWT handling
- **Repositories**: Data access layer using Spring Data JPA
- **Entities**: JPA entities for database mapping
- **DTOs**: Data transfer objects for API responses

## Error Handling

The server returns FHIR-compliant OperationOutcome responses for errors:

```json
{
  "resourceType": "OperationOutcome",
  "id": "message-id",
  "issue": [{
    "severity": "error",
    "details": {
      "text": "Error description"
    }
  }]
}
```

## Contributing

1. Follow the existing code style (single-line comments only)
2. Add tests for new functionality
3. Update documentation as needed

## License

This project is for educational and development purposes.
